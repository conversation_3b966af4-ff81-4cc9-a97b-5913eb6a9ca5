/**
 * Loading Spinner Component
 */
import {
  Box,
  CircularProgress,
  Typography,
  Backdrop,
} from '@mui/material';

/**
 * LoadingSpinner component for showing loading states
 * @param {Object} props - Component props
 * @param {boolean} props.loading - Whether to show the spinner
 * @param {string} props.message - Optional loading message
 * @param {string} props.size - Size of the spinner (small, medium, large)
 * @param {boolean} props.overlay - Whether to show as overlay
 * @param {string} props.color - Color of the spinner
 */
const LoadingSpinner = ({
  loading = false,
  message = 'Loading...',
  size = 'medium',
  overlay = false,
  color = 'primary',
}) => {
  if (!loading) return null;

  const getSizeValue = () => {
    switch (size) {
      case 'small':
        return 24;
      case 'large':
        return 60;
      default:
        return 40;
    }
  };

  const spinnerContent = (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      gap={2}
    >
      <CircularProgress
        size={getSizeValue()}
        color={color}
        thickness={4}
      />
      {message && (
        <Typography
          variant="body2"
          color="text.secondary"
          textAlign="center"
        >
          {message}
        </Typography>
      )}
    </Box>
  );

  if (overlay) {
    return (
      <Backdrop
        sx={{
          color: '#fff',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        }}
        open={loading}
      >
        {spinnerContent}
      </Backdrop>
    );
  }

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      minHeight="200px"
      width="100%"
    >
      {spinnerContent}
    </Box>
  );
};

export default LoadingSpinner;
