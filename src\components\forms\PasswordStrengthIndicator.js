/**
 * Password Strength Indicator Component
 */
import {
  Box,
  LinearProgress,
  Typography,
  Chip,
} from '@mui/material';
import { calculatePasswordStrength } from '../../utils/validation';

/**
 * PasswordStrengthIndicator component for showing password strength
 * @param {Object} props - Component props
 * @param {string} props.password - Password to evaluate
 * @param {boolean} props.show - Whether to show the indicator
 */
const PasswordStrengthIndicator = ({ password, show = true }) => {
  if (!show || !password) return null;

  const { score, strength, color, feedback } = calculatePasswordStrength(password);

  const getProgressColor = () => {
    switch (color) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      default:
        return 'primary';
    }
  };

  const getChipColor = () => {
    switch (color) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ mt: 1, mb: 1 }}>
      {/* Progress Bar */}
      <Box sx={{ mb: 1 }}>
        <LinearProgress
          variant="determinate"
          value={score}
          color={getProgressColor()}
          sx={{
            height: 6,
            borderRadius: 3,
            backgroundColor: 'grey.200',
          }}
        />
      </Box>

      {/* Strength Label and Feedback */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        flexWrap="wrap"
        gap={1}
      >
        <Chip
          label={strength}
          size="small"
          color={getChipColor()}
          variant="outlined"
        />
        
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{
            fontSize: '0.75rem',
            textAlign: 'right',
            flex: 1,
            minWidth: 0,
          }}
        >
          {feedback}
        </Typography>
      </Box>

      {/* Password Requirements (for weak passwords) */}
      {score < 80 && (
        <Box sx={{ mt: 1 }}>
          <Typography variant="caption" color="text.secondary" display="block">
            Password should contain:
          </Typography>
          <Box component="ul" sx={{ 
            m: 0, 
            pl: 2, 
            fontSize: '0.7rem',
            color: 'text.secondary',
            '& li': {
              mb: 0.25
            }
          }}>
            <li>At least 8 characters</li>
            <li>One uppercase letter (A-Z)</li>
            <li>One lowercase letter (a-z)</li>
            <li>One number (0-9)</li>
            <li>One special character (!@#$%^&*)</li>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default PasswordStrengthIndicator;
