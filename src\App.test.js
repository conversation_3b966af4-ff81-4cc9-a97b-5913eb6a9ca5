// Basic tests for the authentication app
import { isValidEmail, calculatePasswordStrength } from './utils/validation';

test('validation utilities work correctly', () => {
  expect(isValidEmail('<EMAIL>')).toBe(true);
  expect(isValidEmail('invalid-email')).toBe(false);
});

test('password strength calculation works', () => {
  const weak = calculatePasswordStrength('password');
  expect(weak.strength).toBe('Medium');

  const strong = calculatePasswordStrength('Password123!');
  expect(strong.strength).toBe('Strong');
});
