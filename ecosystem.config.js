module.exports = {
  apps: [{
    name: 'auth-app-dev',
    script: 'npm',
    args: 'run start:dev',
    watch: true,
    env: {
      NODE_ENV: 'development',
      PORT: 3003 // Specify the development port here
      // You can add more environment variables here if necessary
    }
  }, {
    name: 'auth-app-prod',
    script: 'npm',
    args: 'run build && serve -s build',
    // instances: 'max', // for production, you might want to run multiple instances
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3004 // Specify the production port here
      // You can add more environment variables here if necessary
    }
  }]
};
