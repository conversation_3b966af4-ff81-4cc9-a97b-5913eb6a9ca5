/**
 * Validation utility functions for form validation
 */

/**
 * Email validation regex pattern
 */
export const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

/**
 * Password validation regex patterns
 */
export const PASSWORD_PATTERNS = {
  minLength: /.{8,}/,
  hasUppercase: /[A-Z]/,
  hasLowercase: /[a-z]/,
  hasNumber: /\d/,
  hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/
};

/**
 * Validation rules for react-hook-form
 */
export const validationRules = {
  email: {
    required: 'Email is required',
    pattern: {
      value: EMAIL_REGEX,
      message: 'Please enter a valid email address'
    }
  },
  
  password: {
    required: 'Password is required',
    minLength: {
      value: 8,
      message: 'Password must be at least 8 characters long'
    },
    validate: {
      hasUppercase: (value) => 
        PASSWORD_PATTERNS.hasUppercase.test(value) || 
        'Password must contain at least one uppercase letter',
      hasLowercase: (value) => 
        PASSWORD_PATTERNS.hasLowercase.test(value) || 
        'Password must contain at least one lowercase letter',
      hasNumber: (value) => 
        PASSWORD_PATTERNS.hasNumber.test(value) || 
        'Password must contain at least one number'
    }
  },
  
  confirmPassword: (password) => ({
    required: 'Please confirm your password',
    validate: (value) => 
      value === password || 'Passwords do not match'
  }),
  
  fullName: {
    required: 'Full name is required',
    minLength: {
      value: 2,
      message: 'Full name must be at least 2 characters long'
    },
    pattern: {
      value: /^[a-zA-Z\s]+$/,
      message: 'Full name can only contain letters and spaces'
    }
  },
  
  termsAccepted: {
    required: 'You must accept the terms and conditions'
  }
};

/**
 * Calculate password strength score
 * @param {string} password - The password to evaluate
 * @returns {object} - Score and feedback
 */
export const calculatePasswordStrength = (password) => {
  if (!password) {
    return { score: 0, feedback: 'Enter a password', color: 'error' };
  }
  
  let score = 0;
  const feedback = [];
  
  // Length check
  if (password.length >= 8) {
    score += 20;
  } else {
    feedback.push('At least 8 characters');
  }
  
  // Uppercase check
  if (PASSWORD_PATTERNS.hasUppercase.test(password)) {
    score += 20;
  } else {
    feedback.push('One uppercase letter');
  }
  
  // Lowercase check
  if (PASSWORD_PATTERNS.hasLowercase.test(password)) {
    score += 20;
  } else {
    feedback.push('One lowercase letter');
  }
  
  // Number check
  if (PASSWORD_PATTERNS.hasNumber.test(password)) {
    score += 20;
  } else {
    feedback.push('One number');
  }
  
  // Special character check
  if (PASSWORD_PATTERNS.hasSpecialChar.test(password)) {
    score += 20;
  } else {
    feedback.push('One special character');
  }
  
  // Determine strength level and color
  let strength, color;
  if (score < 40) {
    strength = 'Weak';
    color = 'error';
  } else if (score < 80) {
    strength = 'Medium';
    color = 'warning';
  } else {
    strength = 'Strong';
    color = 'success';
  }
  
  return {
    score,
    strength,
    color,
    feedback: feedback.length > 0 ? `Missing: ${feedback.join(', ')}` : 'Great password!'
  };
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} - True if valid
 */
export const isValidEmail = (email) => {
  return EMAIL_REGEX.test(email);
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {boolean} - True if meets minimum requirements
 */
export const isStrongPassword = (password) => {
  return PASSWORD_PATTERNS.minLength.test(password) &&
         PASSWORD_PATTERNS.hasUppercase.test(password) &&
         PASSWORD_PATTERNS.hasLowercase.test(password) &&
         PASSWORD_PATTERNS.hasNumber.test(password);
};
