# React.js Authentication Application

A complete authentication application built with React.js, Material-UI (MUI), and modern web development best practices. This application demonstrates user authentication flows including login, registration, password reset, and protected routing.

## 🚀 Features

### ✅ **Complete Authentication System**
- User login with email and password
- User registration with validation
- Forgot password functionality
- Social login UI (Google, Facebook)
- Protected routes and route guards
- Persistent authentication state

### ✅ **Modern UI/UX**
- Material-UI (MUI) v5.x components
- Responsive design for all screen sizes
- Loading states and error handling
- Password strength indicator
- Form validation with real-time feedback
- Smooth animations and transitions

### ✅ **Form Validation**
- React Hook Form integration
- Email format validation
- Password strength requirements
- Confirm password matching
- Terms and conditions acceptance
- Real-time validation feedback

### ✅ **State Management**
- React Context API for authentication
- localStorage persistence
- Session management
- Error state handling
- Loading state management

### ✅ **Developer Experience**
- TypeScript-ready codebase
- Component-based architecture
- Reusable utility functions
- Error boundaries
- Clean code structure

## 🛠️ Technology Stack

- **Frontend Framework**: React.js 19.x
- **UI Library**: Material-UI (MUI) v5.x
- **Routing**: React Router v6.x
- **Form Handling**: React Hook Form
- **HTTP Client**: Axios
- **State Management**: React Context API
- **Styling**: Emotion (CSS-in-JS)
- **Icons**: Material-UI Icons
- **Build Tool**: Create React App

## 📦 Installation

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn package manager

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd auth-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Demo Credentials

For testing the application, use these demo credentials:

**Login:**
- Email: `<EMAIL>`
- Password: `Demo123!`

## 📁 Project Structure

```
src/
├── components/
│   ├── common/
│   │   ├── ErrorBoundary.js      # Error boundary component
│   │   ├── LoadingSpinner.js     # Loading spinner component
│   │   └── ProtectedRoute.js     # Route protection component
│   └── forms/
│       └── PasswordStrengthIndicator.js  # Password strength meter
├── context/
│   └── AuthContext.js            # Authentication context
├── pages/
│   ├── login/
│   │   └── index.js              # Login page
│   ├── register/
│   │   └── index.js              # Registration page
│   ├── forgot-password/
│   │   └── index.js              # Forgot password page
│   └── dashboard/
│       └── index.js              # Dashboard page
├── services/
│   └── api.js                    # API service configuration
├── utils/
│   └── validation.js             # Validation utilities
├── App.js                        # Main app component
└── index.js                      # App entry point
```

## 🔧 Available Scripts

### Development

```bash
# Start development server
npm start

# Run tests
npm test

# Build for production
npm run build

# Eject from Create React App (irreversible)
npm run eject
```

## 🌐 API Endpoints

The application uses mock API endpoints for demonstration. In a production environment, replace these with real backend endpoints:

### Authentication Endpoints

```javascript
POST /auth/login
POST /auth/register
POST /auth/forgot-password
POST /auth/social-login
POST /auth/logout
GET  /user/profile
```

### Request/Response Examples

**Login Request:**
```json
{
  "email": "<EMAIL>",
  "password": "Demo123!"
}
```

**Login Response:**
```json
{
  "success": true,
  "token": "jwt-token-here",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "fullName": "Demo User",
    "avatar": null
  }
}
```

## 🔐 Security Features

- **Password Requirements**: Minimum 8 characters with uppercase, lowercase, and numbers
- **JWT Token Management**: Secure token storage and automatic refresh
- **Route Protection**: Authenticated routes with automatic redirects
- **Form Validation**: Client-side validation with security best practices
- **Error Handling**: Secure error messages without sensitive information exposure

## 🎨 Customization

### Theme Configuration

The application uses Material-UI's theming system. Customize colors, typography, and components in `src/App.js`:

```javascript
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2', // Change primary color
    },
    secondary: {
      main: '#dc004e', // Change secondary color
    },
  },
  // Add more customizations
});
```

### Environment Variables

Create a `.env` file in the root directory:

```env
REACT_APP_API_URL=https://your-api-url.com
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id
REACT_APP_FACEBOOK_APP_ID=your-facebook-app-id
```

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

### Deploy to Netlify

1. Build the project: `npm run build`
2. Upload the `build` folder to Netlify
3. Configure redirects for SPA routing

### Deploy to Vercel

```bash
npm install -g vercel
vercel --prod
```

### Deploy to GitHub Pages

```bash
npm install --save-dev gh-pages
npm run build
npm run deploy
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch
```

### Test Structure

```
src/
├── __tests__/
│   ├── components/
│   ├── pages/
│   ├── utils/
│   └── services/
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [Issues](https://github.com/your-repo/auth-app/issues) page
2. Create a new issue with detailed information
3. Contact support at: <EMAIL>

## 🙏 Acknowledgments

- [React.js](https://reactjs.org/) - Frontend framework
- [Material-UI](https://mui.com/) - UI component library
- [React Hook Form](https://react-hook-form.com/) - Form handling
- [React Router](https://reactrouter.com/) - Routing solution
- [Axios](https://axios-http.com/) - HTTP client

---

**Made with ❤️ by [Your Name]**
