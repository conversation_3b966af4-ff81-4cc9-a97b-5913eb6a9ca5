/**
 * Protected Route Component for route authentication
 */
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import LoadingSpinner from './LoadingSpinner';

/**
 * ProtectedRoute component that requires authentication
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authenticated
 * @param {string} props.redirectTo - Path to redirect to if not authenticated
 * @param {boolean} props.requireAuth - Whether authentication is required (default: true)
 */
const ProtectedRoute = ({ 
  children, 
  redirectTo = '/login',
  requireAuth = true 
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <LoadingSpinner
        loading={true}
        message="Checking authentication..."
        overlay={true}
      />
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    // Redirect to login with the current location as state
    // so we can redirect back after login
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location }}
        replace
      />
    );
  }

  // If authentication is not required but user is authenticated
  // (for login/register pages when user is already logged in)
  if (!requireAuth && isAuthenticated) {
    // Get the intended destination from location state or default to dashboard
    const from = location.state?.from?.pathname || '/dashboard';
    return <Navigate to={from} replace />;
  }

  // Render children if authentication requirements are met
  return children;
};

/**
 * PublicRoute component for pages that should not be accessible when authenticated
 * (like login, register pages)
 */
export const PublicRoute = ({ children, redirectTo = '/dashboard' }) => {
  return (
    <ProtectedRoute
      requireAuth={false}
      redirectTo={redirectTo}
    >
      {children}
    </ProtectedRoute>
  );
};

export default ProtectedRoute;
