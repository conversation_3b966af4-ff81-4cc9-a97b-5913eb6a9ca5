/**
 * Dashboard Page Component
 */
import {
  Box,
  Paper,
  Typography,
  Button,
  Container,
  Avatar,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import {
  Logout,
  Person,
  Dashboard as DashboardIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const DashboardPage = () => {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'rgba(255, 255, 255, 0.2)',
              }}
            >
              {user?.avatar ? (
                <img src={user.avatar} alt={user.fullName} />
              ) : (
                <Person fontSize="large" />
              )}
            </Avatar>
            <Box>
              <Typography variant="h4" gutterBottom>
                Welcome back, {user?.fullName || 'User'}!
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                {user?.email}
              </Typography>
            </Box>
          </Box>
          <Button
            variant="outlined"
            color="inherit"
            startIcon={<Logout />}
            onClick={handleLogout}
            sx={{
              borderColor: 'rgba(255, 255, 255, 0.5)',
              '&:hover': {
                borderColor: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            Logout
          </Button>
        </Box>
      </Paper>

      {/* Dashboard Content */}
      <Grid container spacing={3}>
        {/* Main Content Card */}
        <Grid item xs={12}>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <DashboardIcon color="primary" />
                <Typography variant="h6">
                  Project Overview
                </Typography>
              </Box>
              <Typography variant="body1" paragraph>
                This project is created using Augment, a powerful AI-driven development platform that accelerates
                application development with intelligent code generation and modern best practices.
              </Typography>
              <Typography variant="body1" paragraph>
                The application will be deployed on a Minikube Kubernetes cluster, providing a scalable and
                containerized environment for development and testing. This setup demonstrates modern DevOps
                practices including container orchestration and microservices architecture.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Key technologies used: React.js, Material-UI, React Hook Form, Axios, React Router,
                and Kubernetes for deployment orchestration.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default DashboardPage;
