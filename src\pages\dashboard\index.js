/**
 * Dashboard Page Component
 */
import {
  Box,
  Paper,
  Typography,
  Button,
  Container,
  Avatar,
  Grid,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import {
  Logout,
  Person,
  Security,
  Dashboard as DashboardIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const DashboardPage = () => {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'rgba(255, 255, 255, 0.2)',
              }}
            >
              {user?.avatar ? (
                <img src={user.avatar} alt={user.fullName} />
              ) : (
                <Person fontSize="large" />
              )}
            </Avatar>
            <Box>
              <Typography variant="h4" gutterBottom>
                Welcome back, {user?.fullName || 'User'}!
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                {user?.email}
              </Typography>
            </Box>
          </Box>
          <Button
            variant="outlined"
            color="inherit"
            startIcon={<Logout />}
            onClick={handleLogout}
            sx={{
              borderColor: 'rgba(255, 255, 255, 0.5)',
              '&:hover': {
                borderColor: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            Logout
          </Button>
        </Box>
      </Paper>

      {/* Dashboard Content */}
      <Grid container spacing={3}>
        {/* Welcome Card */}
        <Grid item xs={12} md={8}>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <DashboardIcon color="primary" />
                <Typography variant="h6">
                  Dashboard
                </Typography>
              </Box>
              <Typography variant="body1" paragraph>
                Congratulations! You have successfully logged into the authentication demo application.
                This dashboard represents a protected area that requires authentication to access.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This is a demo application showcasing React.js authentication with Material-UI components.
                In a real application, this dashboard would contain your application's main features and data.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* User Info Card */}
        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <Security color="primary" />
                <Typography variant="h6">
                  Account Status
                </Typography>
              </Box>
              <Box display="flex" flexDirection="column" gap={2}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Authentication Status
                  </Typography>
                  <Chip
                    label="Authenticated"
                    color="success"
                    size="small"
                    sx={{ mt: 0.5 }}
                  />
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    User ID
                  </Typography>
                  <Typography variant="body2">
                    {user?.id || 'N/A'}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Login Method
                  </Typography>
                  <Typography variant="body2">
                    Email & Password
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Features Card */}
        <Grid item xs={12}>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Application Features
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box p={2} bgcolor="grey.50" borderRadius={1}>
                    <Typography variant="subtitle2" gutterBottom>
                      ✅ User Authentication
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Login, Register, Forgot Password
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box p={2} bgcolor="grey.50" borderRadius={1}>
                    <Typography variant="subtitle2" gutterBottom>
                      ✅ Form Validation
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      React Hook Form with validation
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box p={2} bgcolor="grey.50" borderRadius={1}>
                    <Typography variant="subtitle2" gutterBottom>
                      ✅ Protected Routes
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Route-based authentication
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box p={2} bgcolor="grey.50" borderRadius={1}>
                    <Typography variant="subtitle2" gutterBottom>
                      ✅ Material-UI Design
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Modern, responsive interface
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default DashboardPage;
