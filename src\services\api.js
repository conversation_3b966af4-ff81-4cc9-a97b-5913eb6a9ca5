/**
 * API service configuration and endpoints
 */
import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'https://jsonplaceholder.typicode.com',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    
    // Network error handling
    if (!error.response) {
      error.message = 'Network error. Please check your connection.';
    }
    
    return Promise.reject(error);
  }
);

/**
 * Authentication API endpoints
 */
export const authAPI = {
  /**
   * User login
   * @param {Object} credentials - Email and password
   * @returns {Promise} - API response
   */
  login: async (credentials) => {
    try {
      // Mock API call - replace with real endpoint
      const response = await new Promise((resolve) => {
        setTimeout(() => {
          if (credentials.email === '<EMAIL>' && credentials.password === 'Demo123!') {
            resolve({
              data: {
                success: true,
                token: 'mock-jwt-token-' + Date.now(),
                user: {
                  id: 1,
                  email: credentials.email,
                  fullName: 'Demo User',
                  avatar: null
                }
              }
            });
          } else {
            throw new Error('Invalid credentials');
          }
        }, 1000);
      });
      
      return response;
    } catch (error) {
      throw new Error(error.message || 'Login failed');
    }
  },

  /**
   * User registration
   * @param {Object} userData - User registration data
   * @returns {Promise} - API response
   */
  register: async (userData) => {
    try {
      // Mock API call - replace with real endpoint
      const response = await new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              success: true,
              message: 'Registration successful',
              user: {
                id: Date.now(),
                email: userData.email,
                fullName: userData.fullName,
                avatar: null
              }
            }
          });
        }, 1500);
      });
      
      return response;
    } catch (error) {
      throw new Error(error.message || 'Registration failed');
    }
  },

  /**
   * Forgot password
   * @param {string} email - User email
   * @returns {Promise} - API response
   */
  forgotPassword: async (email) => {
    try {
      // Mock API call - replace with real endpoint
      const response = await new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              success: true,
              message: 'Password reset link sent to your email'
            }
          });
        }, 1000);
      });
      
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to send reset email');
    }
  },

  /**
   * Social login (Google/Facebook)
   * @param {Object} socialData - Social login data
   * @returns {Promise} - API response
   */
  socialLogin: async (socialData) => {
    try {
      // Mock API call - replace with real endpoint
      const response = await new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              success: true,
              token: 'mock-social-jwt-token-' + Date.now(),
              user: {
                id: Date.now(),
                email: socialData.email,
                fullName: socialData.name,
                avatar: socialData.picture
              }
            }
          });
        }, 1000);
      });
      
      return response;
    } catch (error) {
      throw new Error(error.message || 'Social login failed');
    }
  },

  /**
   * Logout user
   * @returns {Promise} - API response
   */
  logout: async () => {
    try {
      // Mock API call - replace with real endpoint
      const response = await new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              success: true,
              message: 'Logged out successfully'
            }
          });
        }, 500);
      });
      
      return response;
    } catch (error) {
      throw new Error(error.message || 'Logout failed');
    }
  },

  /**
   * Get user profile
   * @returns {Promise} - API response
   */
  getProfile: async () => {
    try {
      // Mock API call - replace with real endpoint
      const response = await api.get('/user/profile');
      return response;
    } catch (error) {
      throw new Error(error.message || 'Failed to fetch profile');
    }
  }
};

/**
 * Generic API error handler
 * @param {Error} error - API error
 * @returns {string} - User-friendly error message
 */
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        return data.message || 'Invalid request. Please check your input.';
      case 401:
        return 'Authentication failed. Please login again.';
      case 403:
        return 'Access denied. You don\'t have permission for this action.';
      case 404:
        return 'Resource not found.';
      case 422:
        return data.message || 'Validation error. Please check your input.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return data.message || 'An unexpected error occurred.';
    }
  } else if (error.request) {
    // Network error
    return 'Network error. Please check your internet connection.';
  } else {
    // Other error
    return error.message || 'An unexpected error occurred.';
  }
};

export default api;
