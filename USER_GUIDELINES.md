# Development Guidelines for React.js Authentication Application

## 1. UI Component Standards

### Material-UI DataGrid Tables
Always implement MUI `<DataGrid>` components with these standardized properties for consistent table behavior:

```jsx
<DataGrid
  checkboxSelection
  pagination
  pageSize={pageSize}
  page={page - 1}
  rowsPerPageOptions={rowsPerPageOptions}
  rowCount={rowCount}
  paginationMode="server"
  onPageChange={handlePageChange}
  onPageSizeChange={handlePageSizeChange}
  rowHeight={38}
  headerHeight={38}
  loading={loading}
  disableSelectionOnClick
  sx={{
    '& .MuiDataGrid-cell': {
      borderBottom: '1px solid #e0e0e0',
    },
  }}
/>
```

Required state variables for DataGrid implementation:
```jsx
const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
const [page, setPage] = useState(1);
const [rowCount, setRowCount] = useState(0);
const [loading, setLoading] = useState(false);
```

### Form Components Standards
- Use `SelectAutoComplete` component for all dropdown selections instead of standard MUI Select
- When implementing SelectAutoComplete for template selection, always save the 'key' property instead of 'value' property in component state
- Include comprehensive validation using react-hook-form with proper error handling and user feedback
- Apply `size="small"` to all TextField components for consistent UI density
- Use InputAdornment with appropriate icons for enhanced user experience

### TextField Implementation Pattern
```jsx
<TextField
  {...register('fieldName', validationRules.fieldName)}
  margin="normal"
  required
  fullWidth
  size="small"
  label="Field Label"
  error={!!errors.fieldName}
  helperText={errors.fieldName?.message}
  InputProps={{
    startAdornment: (
      <InputAdornment position="start">
        <IconComponent color="action" />
      </InputAdornment>
    ),
  }}
/>
```

## 2. Communication Features Implementation

### WhatsApp/Email Integration Pattern
Implement communication features using this standardized pattern for consistency across the application:

```jsx
// State management for communication features
const [communicationMode, setCommunicationMode] = useState(""); // "WHATSAPP" or "EMAIL"
const [sendMessageDialog, setSendMessageDialog] = useState(false);
const [selectedTemplateName, setSelectedTemplateName] = useState("");
const [isLoading, setIsLoading] = useState(false);

// Event handler functions
const handleSendWhatsAppClick = () => {
  setCommunicationMode("WHATSAPP");
  setSendMessageDialog(true);
};

const handleSendEmailClick = () => {
  setCommunicationMode("EMAIL");
  setSendMessageDialog(true);
};

// API communication handler with proper error handling
const handleSendMessage = async () => {
  setIsLoading(true);
  try {
    const url = `${getUrl(authConfig.endpoint)}/send-communication/${targetId}`;
    const data = {
      communicationModeEnums: communicationMode,
      templateIdOrName: selectedTemplateName,
    };
    
    const response = await axios.post(url, data, {
      headers: getAuthorizationHeaders(),
    });
    
    // Handle success response
    handleSuccess(response.data.message);
  } catch (error) {
    handleError(error);
  } finally {
    setIsLoading(false);
    setSendMessageDialog(false);
  }
};
```

## 3. File Upload and Preview Handlers

### Standardized File Preview Implementation
```jsx
const [selectedFileIndex, setSelectedFileIndex] = useState(null);
const [docxContent, setDocxContent] = useState("");
const [pdfUrl, setPdfUrl] = useState(null);

const openDialog = async (index) => {
  setSelectedFileIndex(index);
  const file = selectedFiles[index];

  // Handle DOCX files
  if (file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document") {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      setDocxContent(result.value);
    } catch (error) {
      console.error("Error converting DOCX to HTML:", error);
      showErrorMessage("Failed to preview document");
    }
  }

  // Handle PDF files
  if (file.type === "application/pdf") {
    try {
      const url = URL.createObjectURL(file);
      setPdfUrl(url);
    } catch (error) {
      console.error("Error creating object URL for PDF:", error);
      showErrorMessage("Failed to preview PDF");
    }
  }
};

const closeDialog = () => {
  setSelectedFileIndex(null);
  setDocxContent("");
  if (pdfUrl) {
    URL.revokeObjectURL(pdfUrl);
    setPdfUrl(null);
  }
};
```

## 4. API Integration Standards

### Data Fetching with Dynamic Filters
```jsx
const fetchData = async (currentPage, currentPageSize, selectedFilters = []) => {
  setLoading(true);
  
  try {
    const requestData = {
      page: currentPage,
      pageSize: currentPageSize,
    };

    // Dynamically add filters to request payload
    selectedFilters.forEach((filter) => {
      requestData[filter.key] = filter.value;
    });

    const response = await axios.post(
      getUrl(authConfig.endpoint),
      requestData,
      {
        headers: getAuthorizationHeaders(),
      }
    );
    
    setData(response.data.content);
    setRowCount(response.data.totalElements);
  } catch (error) {
    console.error("Error fetching data:", error);
    showErrorMessage("Failed to load data");
  } finally {
    setLoading(false);
  }
};
```

### Export Functionality Implementation
```jsx
const handleExport = async () => {
  if (selectedRows.length === 0) {
    showWarningMessage("Please select rows to export");
    return;
  }

  setExporting(true);
  try {
    const today = new Date();
    const formattedDate = `${today.getDate().toString().padStart(2, "0")}-${(today.getMonth() + 1).toString().padStart(2, "0")}-${today.getFullYear()}`;

    const response = await axios.post(
      getUrl(authConfig.exportEndpoint),
      selectedRows,
      {
        headers: getAuthorizationHeaders(),
        responseType: "arraybuffer",
      }
    );
    
    const workbook = XLSX.read(response.data, { type: "array" });
    const fileName = `export-${formattedDate}.xlsx`;
    XLSX.writeFile(workbook, fileName);
    
    showSuccessMessage("Export completed successfully");
  } catch (error) {
    console.error("Error exporting:", error);
    showErrorMessage("Export failed");
  } finally {
    setExporting(false);
  }
};
```

## 5. State Management Patterns

### Filter Management Implementation
```jsx
const [selectedFilters, setSelectedFilters] = useState([]);
const [searchingState, setSearchingState] = useState(false);

const handleApplyFilters = (filters) => {
  setSelectedFilters(filters);
  setSearchingState(true);
  setPage(1); // Reset pagination when applying filters
};

const handleRemoveFilter = (filterKey) => {
  setSelectedFilters((prevFilters) =>
    prevFilters.filter((filter) => filter.key !== filterKey)
  );
};

const clearAllFilters = () => {
  setSelectedFilters([]);
  setSearchingState(false);
  setPage(1);
};
```

### Pagination Event Handlers
```jsx
const handlePageChange = (newPage) => {
  setPage(newPage + 1); // DataGrid uses 0-based indexing, convert to 1-based
};

const handlePageSizeChange = (newPageSize) => {
  setPageSize(newPageSize);
  setPage(1); // Always reset to first page when changing page size
};
```

## 6. Error Handling & User Feedback

### Standardized Message Status Pattern
```jsx
const [dialogMessage, setDialogMessage] = useState("");
const [messageStatus, setMessageStatus] = useState("");

const handleSuccess = (message = "Operation completed successfully") => {
  const formattedMessage = `
    <div>
      <h3>${message}</h3>
    </div>
  `;
  setDialogMessage(formattedMessage);
  setMessageStatus("success");
};

const handleError = (error) => {
  const errorMessage = error.response?.data?.message || "Operation failed. Please try again later.";
  const formattedMessage = `
    <div>
      <h3>${errorMessage}</h3>
    </div>
  `;
  setDialogMessage(formattedMessage);
  setMessageStatus("error");
};
```

## 7. Component Organization Standards

### Modular Component Structure
Extract and organize reusable components:
- **Header Components**: For consistent page action layouts
- **FilterChips**: For standardized filter display and management
- **DataGrid Wrappers**: For consistent table behavior across pages
- **Dialog Components**: For modal interactions and confirmations
- **StatusDialog**: For standardized user feedback and notifications
- **LoadingSpinner**: For consistent loading state presentation

### Component File Structure
```
src/
├── components/
│   ├── common/
│   │   ├── LoadingSpinner.js
│   │   ├── ErrorBoundary.js
│   │   └── ProtectedRoute.js
│   ├── forms/
│   │   └── PasswordStrengthIndicator.js
│   └── layout/
│       ├── Header.js
│       └── Navigation.js
```

## 8. Code Quality Standards

### Required Development Practices
- **Error Handling**: Always implement try-catch blocks for all async operations
- **Naming Conventions**: Use camelCase for variables/functions, PascalCase for React components
- **Loading States**: Include loading indicators for all async operations and user interactions
- **Cleanup**: Implement proper cleanup in useEffect hooks to prevent memory leaks
- **Validation**: Use react-hook-form with comprehensive validation rules

### Performance Optimization Guidelines
- Use `useCallback` for event handlers passed to child components to prevent unnecessary re-renders
- Implement `useMemo` for expensive calculations and complex data transformations
- Avoid inline object/array creation in render methods to maintain referential equality
- Use proper dependency arrays in useEffect hooks to control re-execution
- Implement React.memo for components that receive stable props

## 9. Security & Authorization Implementation

### Role-Based Access Control (RBAC) Integration
```jsx
const canAccess = (requiredPermission) =>
  canMenuPage(MENUS.LEFT, PAGES.PAGE_NAME, requiredPermission);

useEffect(() => {
  if (rbacRoles != null && rbacRoles.length > 0) {
    if (!canAccess(PERMISSIONS.READ)) {
      router.push("/401");
      return;
    }
    // Initialize page data only after permission check
    initializePageData();
  }
}, [rbacRoles]);
```

## 10. Testing Guidelines

### Required Test Coverage
- **Unit Tests**: For all handler functions and utility methods
- **Integration Tests**: For API calls and data flow
- **Component Tests**: For rendering and user interactions
- **Form Tests**: For validation and submission workflows
- **Error Boundary Tests**: For error handling scenarios

### Testing Implementation Pattern
```jsx
// Example test structure
describe('ComponentName', () => {
  test('should render without crashing', () => {
    render(<ComponentName />);
  });

  test('should handle form submission correctly', async () => {
    // Test implementation
  });

  test('should display error message on API failure', async () => {
    // Test implementation
  });
});
```

## 11. Handler Function Categories

### Event Handler Patterns
- **File Upload Dialogs**: Use `openDialog` and `closeDialog` pattern with proper state management
- **Form Submissions**: Include validation, loading states, and comprehensive error handling
- **Button Interactions**: Implement loading states and provide immediate user feedback

### API Handler Standards
- **Data Fetching**: Use consistent error handling with try-catch blocks and user notifications
- **Communication Endpoints**: Follow `/send-communication/{id}` pattern for messaging features
- **Export Operations**: Use `responseType: "arraybuffer"` for file downloads with proper error handling

### Form Handler Implementation
- **Input Changes**: Use functional state updates for arrays and objects to maintain immutability
- **Dynamic Lists**: Implement add/remove functionality with proper sequence management
- **Template Selection**: Save 'key' property instead of 'value' for SelectAutoComplete components

## 12. Memory and Performance Patterns

### Communication Feature Reusability
- For donor group WhatsApp communication, use endpoint pattern `/send-communication/{groupId}` with payload containing `communicationModeEnums: 'WHATSAPP'` and `templateIdOrName` fields
- For ContactGroupActions component, implement Email messaging using the same pattern with `emailsList` array and `communicationModeEnums: 'EMAIL'`
- When implementing similar communication features (WhatsApp, Email, SMS), reuse existing dialog components and handlers, only changing the `communicationModeEnums` parameter

### File Upload Memory Management
- Use `openDialog` and `closeDialog` pattern for file preview functionality with proper cleanup
- Always include error handling for different file types (PDF, DOCX, images)
- Use `URL.createObjectURL` for PDF preview and `mammoth` library for DOCX conversion
- Implement proper memory cleanup with `URL.revokeObjectURL` to prevent memory leaks

## 13. Authentication Application Specific Guidelines

### Form Validation Standards
- Use react-hook-form for all form implementations
- Implement real-time validation feedback with proper error messages
- Use Material-UI TextField with `size="small"` for consistent UI density
- Include password strength indicators for registration forms

### Routing and Navigation
- Use React Router v6 with proper route protection
- Implement PublicRoute and ProtectedRoute components for access control
- Use location-based keys for proper component re-rendering on route changes

### State Management
- Use React Context API for authentication state management
- Implement localStorage persistence for user sessions
- Use proper error boundaries for component-level error handling

## 14. Best Practices Summary

1. **Consistency**: Follow established patterns across all components and features
2. **Error Handling**: Always include comprehensive try-catch blocks and user feedback mechanisms
3. **Performance**: Use React optimization hooks appropriately and avoid unnecessary re-renders
4. **Security**: Implement proper RBAC checks and input validation
5. **Testing**: Write comprehensive tests for all functionality and edge cases
6. **Documentation**: Keep code well-commented and maintain self-documenting code practices
7. **Modularity**: Extract reusable components and utilities for better maintainability
8. **State Management**: Use functional updates and implement proper cleanup procedures
9. **API Integration**: Follow consistent request/response patterns with proper error handling
10. **User Experience**: Provide loading states, clear feedback, and responsive design across all devices

## 15. Code Review Checklist

Before submitting code for review, ensure:
- [ ] All async operations have proper error handling
- [ ] Loading states are implemented for user interactions
- [ ] Form validation is comprehensive and user-friendly
- [ ] Components are properly organized and reusable
- [ ] Performance optimizations are applied where appropriate
- [ ] Security considerations are addressed
- [ ] Tests are written and passing
- [ ] Code follows established naming conventions
- [ ] Documentation is updated as needed
- [ ] Mobile responsiveness is maintained
