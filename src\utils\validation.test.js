/**
 * Tests for validation utilities
 */
import {
  isValidEmail,
  isStrongPassword,
  calculatePasswordStrength,
  EMAIL_REGEX,
  PASSWORD_PATTERNS,
} from './validation';

describe('Validation Utilities', () => {
  describe('isValidEmail', () => {
    test('should validate correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    test('should reject invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('test.example.com')).toBe(false);
    });
  });

  describe('isStrongPassword', () => {
    test('should validate strong passwords', () => {
      expect(isStrongPassword('Password123')).toBe(true);
      expect(isStrongPassword('MySecure123')).toBe(true);
      expect(isStrongPassword('Test1234')).toBe(true);
    });

    test('should reject weak passwords', () => {
      expect(isStrongPassword('password')).toBe(false); // no uppercase, no number
      expect(isStrongPassword('PASSWORD')).toBe(false); // no lowercase, no number
      expect(isStrongPassword('Password')).toBe(false); // no number
      expect(isStrongPassword('Pass1')).toBe(false); // too short
    });
  });

  describe('calculatePasswordStrength', () => {
    test('should return correct strength for empty password', () => {
      const result = calculatePasswordStrength('');
      expect(result.score).toBe(0);
      expect(result.feedback).toBe('Enter a password');
      expect(result.color).toBe('error');
    });

    test('should return weak for simple passwords', () => {
      const result = calculatePasswordStrength('password');
      expect(result.score).toBe(40); // length + lowercase
      expect(result.strength).toBe('Medium'); // 40 score = Medium
      expect(result.color).toBe('warning');
    });

    test('should return strong for complex passwords', () => {
      const result = calculatePasswordStrength('Password123!');
      expect(result.score).toBe(100);
      expect(result.strength).toBe('Strong');
      expect(result.color).toBe('success');
    });
  });

  describe('EMAIL_REGEX', () => {
    test('should match valid email patterns', () => {
      expect(EMAIL_REGEX.test('<EMAIL>')).toBe(true);
      expect(EMAIL_REGEX.test('<EMAIL>')).toBe(true);
    });

    test('should not match invalid email patterns', () => {
      expect(EMAIL_REGEX.test('invalid-email')).toBe(false);
      expect(EMAIL_REGEX.test('test@')).toBe(false);
    });
  });

  describe('PASSWORD_PATTERNS', () => {
    test('should correctly identify password components', () => {
      const password = 'Password123!';
      
      expect(PASSWORD_PATTERNS.minLength.test(password)).toBe(true);
      expect(PASSWORD_PATTERNS.hasUppercase.test(password)).toBe(true);
      expect(PASSWORD_PATTERNS.hasLowercase.test(password)).toBe(true);
      expect(PASSWORD_PATTERNS.hasNumber.test(password)).toBe(true);
      expect(PASSWORD_PATTERNS.hasSpecialChar.test(password)).toBe(true);
    });
  });
});
